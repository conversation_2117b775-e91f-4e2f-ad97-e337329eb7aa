<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>植物生理学思维导图</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: auto;
        }

        #mindmap {
            width: 100%;
            min-height: 100vh;
            position: relative;
            padding: 100px 50px 50px 50px;
            box-sizing: border-box;
        }

        .title-bar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            z-index: 1000;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .title-bar h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            text-align: center;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        /* 思维导图节点样式 */
        .mindmap-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .root-node {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 24px;
            font-weight: bold;
            padding: 20px 40px;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            text-align: center;
            margin-bottom: 40px;
        }

        .chapter-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 40px;
            width: 100%;
            max-width: 1400px;
        }

        .chapter {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 400px;
            max-width: 450px;
        }

        .chapter-title {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            font-size: 18px;
            font-weight: 600;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .section {
            margin-bottom: 20px;
        }

        .section-title {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .section-title:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .subsection {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .subsection-title {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: #2c3e50;
            font-size: 14px;
            font-weight: 500;
            padding: 10px 14px;
            border-radius: 6px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .subsection-title:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .detail {
            margin-left: 20px;
            margin-bottom: 8px;
        }

        .detail-item {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #2c3e50;
            font-size: 13px;
            padding: 8px 12px;
            border-radius: 5px;
            margin-bottom: 5px;
            line-height: 1.4;
            transition: all 0.3s ease;
        }

        .detail-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .sub-detail {
            margin-left: 15px;
            margin-top: 5px;
        }

        .sub-detail-item {
            background: rgba(255, 255, 255, 0.8);
            color: #2c3e50;
            font-size: 12px;
            padding: 6px 10px;
            border-radius: 4px;
            margin-bottom: 3px;
            line-height: 1.3;
            border-left: 3px solid #667eea;
        }

        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 999;
            text-align: center;
        }

        .collapsible {
            max-height: 1000px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .collapsed {
            max-height: 0;
        }
    </style>
</head>
<body>
    <div class="title-bar">
        <h1>植物生理学思维导图</h1>
    </div>
    
    <div class="controls">
        <button class="control-btn" onclick="expandAll()">展开全部</button>
        <button class="control-btn" onclick="collapseAll()">收起全部</button>
        <button class="control-btn" onclick="scrollToTop()">回到顶部</button>
    </div>

    <div class="loading" id="loading">正在加载思维导图...</div>
    <div id="mindmap">
        <div class="mindmap-container">
            <div class="root-node">植物生理学</div>
            <div class="chapter-container" id="chapters"></div>
        </div>
    </div>
    <script>
        // 思维导图数据结构
        const mindMapData = [
            {
                title: "第七章 植物的生殖生理",
                sections: [
                    {
                        title: "开花诱导：从营养生长到生殖生长的转变",
                        subsections: [
                            {
                                title: "内在条件：达到'花熟状态'",
                                details: [
                                    {
                                        title: "幼年期 (Juvenility)",
                                        items: [
                                            "营养生长阶段，对开花信号不敏感",
                                            "实例：桃三、杏四、梨五年，核桃、白果公孙见"
                                        ]
                                    },
                                    {
                                        title: "花熟状态 (Ripeness to flower)",
                                        items: [
                                            "发育到一定阶段，具备了感受开花信号的能力"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "环境信号的诱导",
                                details: [
                                    {
                                        title: "春化作用 (Vernalization)",
                                        items: [
                                            "概念：植株必须经过一段时间的低温处理才能开花或促进开花的现象",
                                            "感受部位：茎端分生组织",
                                            "有效温度：1~7℃为最有效范围",
                                            "绝对需要型：冬小麦、二年生植物（如萝卜、白菜），不经春化不能开花",
                                            "相对需要型：低温可显著促进开花",
                                            "机制：低温解除开花抑制基因（如拟南芥中的FLC）的表达。赤霉素(GA)在某些植物中可以部分替代低温的作用"
                                        ]
                                    },
                                    {
                                        title: "光周期现象 (Photoperiodism)",
                                        items: [
                                            "概念：植物对昼夜相对长度，特别是夜间长度的反应",
                                            "感光色素：光敏色素 (Phytochrome)，通过其 Pfr/Pr 比例的变化来'计时'",
                                            "关键因素：临界暗期。长夜（未中断的黑暗）是短日植物开花的关键；短夜（或被光中断的长夜）是长日植物开花的关键",
                                            "长日植物 (LDP)：日照 > 临界日长（夜长 < 临界暗期）才开花。如：小麦、菠菜、天仙子",
                                            "短日植物 (SDP)：日照 < 临界日长（夜长 > 临界暗期）才开花。如：大豆、菊花、苍耳",
                                            "日中性植物 (DNP)：开花不受日长影响。如：黄瓜、番茄、玉米",
                                            "感受部位：叶片",
                                            "传递物质：成花素 (Florigen)，通过韧皮部运输到茎顶"
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        title: "花器官的形成与性别分化",
                        subsections: [
                            {
                                title: "花发育的遗传调控：ABCDE模型",
                                details: [
                                    {
                                        title: "基因功能",
                                        items: [
                                            "几类同源异型基因的组合表达，决定了花四轮器官（萼片、花瓣、雄蕊、心皮）的形成",
                                            "A类基因 → 萼片",
                                            "A + B类基因 → 花瓣",
                                            "B + C类基因 → 雄蕊",
                                            "C类基因 → 心皮",
                                            "D类基因 → 胚珠",
                                            "E类基因 → 作为基础，与其他基因协同作用"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "性别分化与调控",
                                details: [
                                    {
                                        title: "影响因素",
                                        items: [
                                            "遗传、年龄、环境因素"
                                        ]
                                    },
                                    {
                                        title: "激素调控",
                                        items: [
                                            "生长素(IAA)和乙烯(ETH)：促进雌花形成",
                                            "赤霉素(GA)：促进雄花形成",
                                            "细胞分裂素(CTK)：有利于雌花形成"
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        title: "受精生理",
                        subsections: [
                            {
                                title: "花粉与柱头的相互识别",
                                details: [
                                    {
                                        title: "识别机制",
                                        items: [
                                            "识别物质：花粉外壁的糖蛋白与柱头表面的识别蛋白",
                                            "自交不亲和性：防止自花受精的遗传机制，由复等位S基因控制"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "双受精 (Double Fertilization)",
                                details: [
                                    {
                                        title: "特点与过程",
                                        items: [
                                            "被子植物特有的生殖方式",
                                            "一个精子 + 卵细胞 → 合子 (2n) → 发育成胚",
                                            "另一个精子 + 中央细胞(含2个极核) → 初生胚乳核 (3n) → 发育成胚乳"
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                title: "第八章 植物的成熟和衰老生理",
                sections: [
                    {
                        title: "成熟生理",
                        subsections: [
                            {
                                title: "种子的发育与成熟",
                                details: [
                                    {
                                        title: "发育特征",
                                        items: [
                                            "物质积累：集中合成和储存淀粉、蛋白质、脂肪等大分子物质",
                                            "激素调控：ABA含量在种子发育后期显著升高，诱导贮藏蛋白合成并促进休眠",
                                            "生理状态：含水量显著下降，代谢活动降到极低水平"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "果实的生长与成熟",
                                details: [
                                    {
                                        title: "生长模式",
                                        items: [
                                            "生长曲线：单S型（苹果、番茄）或双S型（桃、葡萄）"
                                        ]
                                    },
                                    {
                                        title: "呼吸跃变 (Climacteric)",
                                        items: [
                                            "跃变型果实：成熟时呼吸速率急剧上升，伴随大量乙烯释放。如：香蕉、苹果、番茄",
                                            "非跃变型果实：成熟过程呼吸平稳，无高峰。如：葡萄、柑橘、草莓"
                                        ]
                                    },
                                    {
                                        title: "品质变化",
                                        items: [
                                            "颜色：叶绿素降解，类胡萝卜素、花青素合成，果实转色",
                                            "质地：果胶降解，细胞壁软化",
                                            "风味：淀粉转化为糖，有机酸含量下降，芳香物质（酯类）形成"
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        title: "休眠 (Dormancy)",
                        subsections: [
                            {
                                title: "概念与类型",
                                details: [
                                    {
                                        title: "休眠类型",
                                        items: [
                                            "强迫休眠 (Quiescence)：由外界环境条件不适宜（如低温、干旱）引起的生长停顿",
                                            "生理休眠 (True Dormancy)：由植物内部因素控制，即使在适宜环境下也暂时不能生长"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "休眠的成因与解除",
                                details: [
                                    {
                                        title: "种子休眠",
                                        items: [
                                            "原因：种皮限制（不透水、不透气、机械阻碍）、胚未成熟、需要后熟、内含抑制物(ABA)",
                                            "解除方法：层积处理（低温湿沙）、光照、机械破损、流水冲洗"
                                        ]
                                    },
                                    {
                                        title: "芽休眠",
                                        items: [
                                            "诱导因素：短日照和低温。内源ABA含量上升",
                                            "解除：必须满足一定的低温时数（需冷量），才能打破休眠"
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        title: "衰老与脱落",
                        subsections: [
                            {
                                title: "衰老 (Senescence)",
                                details: [
                                    {
                                        title: "基本概念",
                                        items: [
                                            "概念：一个主动的、受基因调控的有序过程，是发育的最终阶段（程序性细胞死亡, PCD）"
                                        ]
                                    },
                                    {
                                        title: "生理生化特征",
                                        items: [
                                            "合成代谢下降，分解代谢增强：光合作用下降，水解酶活性增强",
                                            "膜系统结构与功能丧失：膜透性增加，是衰老的重要指标",
                                            "激素平衡失调：ABA、乙烯水平上升（促进衰老）；CTK、GA、IAA水平下降（延缓衰老）"
                                        ]
                                    },
                                    {
                                        title: "机理假说",
                                        items: [
                                            "基因时空调控假说、自由基损伤假说、植物激素调节假说"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "脱落 (Abscission)",
                                details: [
                                    {
                                        title: "结构与机制",
                                        items: [
                                            "结构基础：在叶柄、果柄基部形成离层",
                                            "细胞学变化：离层细胞中纤维素酶和果胶酶活性增强，分解细胞壁和中胶层"
                                        ]
                                    },
                                    {
                                        title: "激素调控",
                                        items: [
                                            "生长素梯度学说：当器官（叶片）产生的生长素减少，导致离层两侧生长素浓度梯度消失或逆转时，脱落发生",
                                            "乙烯：是脱落的主要促进激素，诱导水解酶的合成"
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                title: "第九章 植物的逆境生理",
                sections: [
                    {
                        title: "逆境胁迫总论",
                        subsections: [
                            {
                                title: "基本概念",
                                details: [
                                    {
                                        title: "定义",
                                        items: [
                                            "逆境 (Stress)：对植物生长和生存不利的环境因素总和",
                                            "抗逆性 (Resistance)：植物抵抗和适应逆境的能力",
                                            "抗性方式：避逆性 (避开逆境) vs. 耐逆性 (忍耐逆境)"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "逆境伤害的共同特征",
                                details: [
                                    {
                                        title: "伤害表现",
                                        items: [
                                            "膜系统损伤：膜脂过氧化，膜透性增大，功能丧失",
                                            "活性氧 (ROS) 累积：产生超氧阴离子、过氧化氢等，造成氧化损伤"
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        title: "植物的通用抗逆机制",
                        subsections: [
                            {
                                title: "渗透调节 (Osmotic Adjustment)",
                                details: [
                                    {
                                        title: "机制与物质",
                                        items: [
                                            "细胞通过主动积累溶质，降低渗透势，维持细胞膨压",
                                            "主要渗透调节物质：脯氨酸、甜菜碱、可溶性糖、无机离子"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "抗氧化系统 (Antioxidant System)",
                                details: [
                                    {
                                        title: "防护系统",
                                        items: [
                                            "酶促系统：超氧化物歧化酶(SOD)、过氧化物酶(POD)、过氧化氢酶(CAT)",
                                            "非酶促系统：抗坏血酸(维生素C)、谷胱甘肽(GSH)、类胡萝卜素、维生素E"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "逆境蛋白 (Stress Proteins) 的合成",
                                details: [
                                    {
                                        title: "蛋白类型",
                                        items: [
                                            "热激蛋白 (HSP)：高温下合成，作为分子伴侣，保护蛋白质结构",
                                            "低温诱导蛋白 (COR/CSP)：保护膜系统，防止细胞过度脱水",
                                            "病程相关蛋白 (PR)：抵抗病原菌侵染"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "激素调控：ABA的核心作用",
                                details: [
                                    {
                                        title: "ABA功能",
                                        items: [
                                            "ABA被称为'逆境激素'，在多种逆境下含量都会迅速增加，一个关键作用是诱导气孔关闭，减少水分散失"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "交叉适应 (Cross Adaptation)",
                                details: [
                                    {
                                        title: "适应现象",
                                        items: [
                                            "经历一种逆境后，对另一种逆境的抵抗能力也得到提高的现象"
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        title: "主要非生物逆境",
                        subsections: [
                            {
                                title: "低温胁迫 (寒害 0℃以上, 冻害 0℃以下)",
                                details: [
                                    {
                                        title: "伤害机制",
                                        items: [
                                            "寒害：膜相变，从流动的液晶态变为固定的凝胶态，功能丧失",
                                            "冻害：胞外结冰，导致细胞严重脱水和机械损伤"
                                        ]
                                    },
                                    {
                                        title: "抗寒性",
                                        items: [
                                            "增加膜脂中不饱和脂肪酸的比例，保持膜的流动性；积累可溶性糖等抗冻物质"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "干旱胁迫 (旱害)",
                                details: [
                                    {
                                        title: "伤害与抗性",
                                        items: [
                                            "伤害机制：细胞失水，代谢紊乱，原生质机械损伤",
                                            "抗旱性：形态上根系发达、叶片小；生理上快速关闭气孔、进行渗透调节"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "盐胁迫 (盐害)",
                                details: [
                                    {
                                        title: "伤害与抗性",
                                        items: [
                                            "伤害机制：渗透胁迫（导致'生理干旱'）和离子毒害",
                                            "抗盐性：拒盐（根部选择性吸收）、泌盐（通过盐腺排出）、区隔化（将离子储存在液泡中）"
                                        ]
                                    }
                                ]
                            },
                            {
                                title: "淹水胁迫 (涝害)",
                                details: [
                                    {
                                        title: "伤害与抗性",
                                        items: [
                                            "伤害核心：缺氧 (Anoxia/Hypoxia)，导致有氧呼吸受阻",
                                            "抗涝性：形成通气组织 (Aerenchyma)，进行代谢适应"
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ];

        // 初始化函数
        function initMindMap() {
            try {
                console.log('开始初始化思维导图...');

                const chaptersContainer = document.getElementById('chapters');
                if (!chaptersContainer) {
                    throw new Error('找不到章节容器');
                }

                // 清空容器
                chaptersContainer.innerHTML = '';

                // 渲染每个章节
                mindMapData.forEach((chapter, chapterIndex) => {
                    const chapterDiv = document.createElement('div');
                    chapterDiv.className = 'chapter';

                    // 章节标题
                    const chapterTitle = document.createElement('div');
                    chapterTitle.className = 'chapter-title';
                    chapterTitle.textContent = chapter.title;
                    chapterDiv.appendChild(chapterTitle);

                    // 渲染章节内容
                    chapter.sections.forEach((section, sectionIndex) => {
                        const sectionDiv = document.createElement('div');
                        sectionDiv.className = 'section';

                        // 节标题
                        const sectionTitle = document.createElement('div');
                        sectionTitle.className = 'section-title';
                        sectionTitle.textContent = section.title;
                        sectionTitle.onclick = () => toggleSection(chapterIndex, sectionIndex);
                        sectionDiv.appendChild(sectionTitle);

                        // 节内容容器
                        const sectionContent = document.createElement('div');
                        sectionContent.className = 'collapsible';
                        sectionContent.id = `section-${chapterIndex}-${sectionIndex}`;

                        // 渲染子节
                        section.subsections.forEach((subsection, subsectionIndex) => {
                            const subsectionDiv = document.createElement('div');
                            subsectionDiv.className = 'subsection';

                            // 子节标题
                            const subsectionTitle = document.createElement('div');
                            subsectionTitle.className = 'subsection-title';
                            subsectionTitle.textContent = subsection.title;
                            subsectionTitle.onclick = () => toggleSubsection(chapterIndex, sectionIndex, subsectionIndex);
                            subsectionDiv.appendChild(subsectionTitle);

                            // 子节内容容器
                            const subsectionContent = document.createElement('div');
                            subsectionContent.className = 'collapsible';
                            subsectionContent.id = `subsection-${chapterIndex}-${sectionIndex}-${subsectionIndex}`;

                            // 渲染详细内容
                            subsection.details.forEach(detail => {
                                const detailDiv = document.createElement('div');
                                detailDiv.className = 'detail';

                                // 详细标题
                                const detailTitle = document.createElement('div');
                                detailTitle.className = 'detail-item';
                                detailTitle.textContent = detail.title;
                                detailDiv.appendChild(detailTitle);

                                // 详细项目
                                const subDetailDiv = document.createElement('div');
                                subDetailDiv.className = 'sub-detail';
                                detail.items.forEach(item => {
                                    const itemDiv = document.createElement('div');
                                    itemDiv.className = 'sub-detail-item';
                                    itemDiv.textContent = item;
                                    subDetailDiv.appendChild(itemDiv);
                                });
                                detailDiv.appendChild(subDetailDiv);

                                subsectionContent.appendChild(detailDiv);
                            });

                            subsectionDiv.appendChild(subsectionContent);
                            sectionContent.appendChild(subsectionDiv);
                        });

                        sectionDiv.appendChild(sectionContent);
                        chapterDiv.appendChild(sectionDiv);
                    });

                    chaptersContainer.appendChild(chapterDiv);
                });

                console.log('思维导图初始化成功');

                // 隐藏加载提示
                const loadingElement = document.getElementById('loading');
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }

            } catch (error) {
                console.error('初始化思维导图时出错:', error);
                const loadingElement = document.getElementById('loading');
                if (loadingElement) {
                    loadingElement.innerHTML = '加载失败: ' + error.message + '<br>请检查网络连接或刷新页面重试';
                    loadingElement.style.color = '#ff6b6b';
                }
            }
        }

        // 切换节的展开/收起状态
        function toggleSection(chapterIndex, sectionIndex) {
            const element = document.getElementById(`section-${chapterIndex}-${sectionIndex}`);
            if (element) {
                element.classList.toggle('collapsed');
            }
        }

        // 切换子节的展开/收起状态
        function toggleSubsection(chapterIndex, sectionIndex, subsectionIndex) {
            const element = document.getElementById(`subsection-${chapterIndex}-${sectionIndex}-${subsectionIndex}`);
            if (element) {
                element.classList.toggle('collapsed');
            }
        }

        // 控制函数
        function expandAll() {
            const collapsibles = document.querySelectorAll('.collapsible');
            collapsibles.forEach(element => {
                element.classList.remove('collapsed');
            });
        }

        function collapseAll() {
            const collapsibles = document.querySelectorAll('.collapsible');
            collapsibles.forEach(element => {
                element.classList.add('collapsed');
            });
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');

            // 直接初始化思维导图
            setTimeout(() => {
                initMindMap();
            }, 500);
        });
    </script>
</body>
</html>
