<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>植物生理学思维导图</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .header {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 15px 30px;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            z-index: 1000;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            text-align: center;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .mindmap-container {
            position: fixed;
            top: 100px;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
        }

        #mindmap {
            width: 100%;
            height: 100%;
            position: relative;
            cursor: grab;
        }

        #mindmap:active {
            cursor: grabbing;
        }

        .mindmap-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transform-origin: center;
            transition: transform 0.3s ease;
        }

        .node {
            position: absolute;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 10px 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            min-width: 120px;
            text-align: center;
            font-size: 12px;
            line-height: 1.4;
            user-select: none;
        }

        .node:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            z-index: 100;
        }

        .root-node {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 18px;
            font-weight: bold;
            padding: 20px 30px;
            border-radius: 15px;
            min-width: 200px;
            top: 0;
            left: 0;
        }

        .chapter-node {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            font-weight: bold;
            font-size: 14px;
            min-width: 160px;
        }

        .section-node {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            font-weight: 600;
            font-size: 12px;
            min-width: 140px;
        }

        .subsection-node {
            background: rgba(255, 255, 255, 0.95);
            color: #444;
            font-size: 11px;
            min-width: 120px;
        }

        .detail-node {
            background: rgba(255, 255, 255, 0.8);
            color: #666;
            font-size: 10px;
            min-width: 100px;
            max-width: 200px;
        }

        .connection-line {
            position: absolute;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
            transform-origin: left center;
            z-index: 1;
            height: 2px;
            border-radius: 1px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .collapsed .node {
            opacity: 0.3;
        }

        .zoom-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .zoom-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .zoom-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
            z-index: 999;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>植物生理学思维导图</h1>
    </div>
    
    <div class="controls">
        <button class="control-btn" onclick="expandAll()">展开全部</button>
        <button class="control-btn" onclick="collapseAll()">收起全部</button>
        <button class="control-btn" onclick="resetView()">重置视图</button>
    </div>

    <div class="loading" id="loading">正在生成思维导图...</div>
    
    <div class="mindmap-container">
        <div id="mindmap">
            <div class="mindmap-content" id="mindmap-content">
                <!-- 思维导图节点将在这里动态生成 -->
            </div>
        </div>
    </div>

    <div class="zoom-controls">
        <button class="zoom-btn" onclick="zoomIn()">+</button>
        <button class="zoom-btn" onclick="zoomOut()">-</button>
    </div>

    <script>
        // 思维导图数据
        const mindMapData = {
            title: "植物生理学",
            children: [
                {
                    title: "第七章 植物的生殖生理",
                    children: [
                        {
                            title: "开花诱导",
                            children: [
                                {
                                    title: "内在条件",
                                    children: [
                                        { title: "幼年期" },
                                        { title: "花熟状态" }
                                    ]
                                },
                                {
                                    title: "环境信号",
                                    children: [
                                        { title: "春化作用" },
                                        { title: "光周期现象" }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "花器官形成",
                            children: [
                                { title: "ABCDE模型" },
                                { title: "性别分化" }
                            ]
                        },
                        {
                            title: "受精生理",
                            children: [
                                { title: "花粉识别" },
                                { title: "双受精" }
                            ]
                        }
                    ]
                },
                {
                    title: "第八章 植物的成熟和衰老生理",
                    children: [
                        {
                            title: "成熟生理",
                            children: [
                                { title: "种子发育与成熟" },
                                { title: "果实生长与成熟" }
                            ]
                        },
                        {
                            title: "休眠",
                            children: [
                                { title: "概念与类型" },
                                { title: "成因与解除" }
                            ]
                        },
                        {
                            title: "衰老与脱落",
                            children: [
                                { title: "衰老机制" },
                                { title: "脱落过程" }
                            ]
                        }
                    ]
                },
                {
                    title: "第九章 植物的逆境生理",
                    children: [
                        {
                            title: "逆境胁迫总论",
                            children: [
                                { title: "基本概念" },
                                { title: "伤害特征" }
                            ]
                        },
                        {
                            title: "通用抗逆机制",
                            children: [
                                { title: "渗透调节" },
                                { title: "抗氧化系统" },
                                { title: "逆境蛋白" }
                            ]
                        },
                        {
                            title: "主要非生物逆境",
                            children: [
                                { title: "低温胁迫" },
                                { title: "干旱胁迫" },
                                { title: "盐胁迫" },
                                { title: "淹水胁迫" }
                            ]
                        }
                    ]
                }
            ]
        };

        let currentScale = 1;
        let isDragging = false;
        let dragStart = { x: 0, y: 0 };
        let currentTranslate = { x: 0, y: 0 };

        // 创建节点
        function createNode(data, x, y, className = 'node') {
            const node = document.createElement('div');
            node.className = `node ${className}`;
            node.textContent = data.title;
            node.style.left = x + 'px';
            node.style.top = y + 'px';
            node.dataset.expanded = 'true';

            // 添加点击事件
            node.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleNode(node, data);
            });

            return node;
        }

        // 创建连接线
        function createLine(x1, y1, x2, y2) {
            const line = document.createElement('div');
            line.className = 'connection-line';

            const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
            const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

            line.style.width = length + 'px';
            line.style.left = x1 + 'px';
            line.style.top = y1 + 'px';
            line.style.transform = `rotate(${angle}deg)`;

            return line;
        }

        // 递归创建子节点
        function createChildNodes(parentData, parentNode, container, level) {
            if (!parentData.children || parentData.children.length === 0) return;

            const parentX = parseInt(parentNode.style.left) + parentNode.offsetWidth / 2;
            const parentY = parseInt(parentNode.style.top) + parentNode.offsetHeight / 2;

            const childCount = parentData.children.length;
            let positions = [];

            if (level === 0) {
                // 第一层：三个主要章节，分布在120度角
                const baseAngle = -90; // 从上方开始
                const angleStep = 120; // 120度间隔
                positions = parentData.children.map((_, index) => {
                    const angle = (baseAngle + angleStep * index) * Math.PI / 180;
                    const radius = 250;
                    return {
                        x: parentX + Math.cos(angle) * radius - 80,
                        y: parentY + Math.sin(angle) * radius - 25
                    };
                });
            } else if (level === 1) {
                // 第二层：围绕父节点分布
                const baseAngle = -90;
                const totalAngle = Math.min(180, childCount * 45); // 最大180度扇形
                const angleStep = childCount > 1 ? totalAngle / (childCount - 1) : 0;
                const startAngle = baseAngle - totalAngle / 2;

                positions = parentData.children.map((_, index) => {
                    const angle = (startAngle + angleStep * index) * Math.PI / 180;
                    const radius = 180;
                    return {
                        x: parentX + Math.cos(angle) * radius - 70,
                        y: parentY + Math.sin(angle) * radius - 20
                    };
                });
            } else {
                // 更深层：紧密分布
                const baseAngle = -90;
                const totalAngle = Math.min(120, childCount * 30);
                const angleStep = childCount > 1 ? totalAngle / (childCount - 1) : 0;
                const startAngle = baseAngle - totalAngle / 2;

                positions = parentData.children.map((_, index) => {
                    const angle = (startAngle + angleStep * index) * Math.PI / 180;
                    const radius = 120;
                    return {
                        x: parentX + Math.cos(angle) * radius - 60,
                        y: parentY + Math.sin(angle) * radius - 15
                    };
                });
            }

            parentData.children.forEach((child, index) => {
                const pos = positions[index];

                // 确定节点类型
                let nodeClass = 'node';
                if (level === 0) nodeClass = 'chapter-node';
                else if (level === 1) nodeClass = 'section-node';
                else if (level === 2) nodeClass = 'subsection-node';
                else nodeClass = 'detail-node';

                const childNode = createNode(child, pos.x, pos.y, nodeClass);
                container.appendChild(childNode);

                // 创建连接线
                const childCenterX = pos.x + childNode.offsetWidth / 2;
                const childCenterY = pos.y + childNode.offsetHeight / 2;

                const line = createLine(parentX, parentY, childCenterX, childCenterY);
                container.appendChild(line);

                // 递归创建更深层的子节点
                if (child.children && child.children.length > 0 && level < 2) {
                    setTimeout(() => {
                        createChildNodes(child, childNode, container, level + 1);
                    }, 50);
                }
            });
        }

        // 切换节点展开/收起
        function toggleNode(node, data) {
            const isExpanded = node.dataset.expanded === 'true';
            node.dataset.expanded = isExpanded ? 'false' : 'true';

            // 添加点击动画效果
            node.style.transform = 'scale(0.95)';
            setTimeout(() => {
                node.style.transform = '';
            }, 150);

            // 改变节点样式表示状态
            if (isExpanded) {
                node.style.opacity = '0.7';
                node.style.filter = 'grayscale(0.3)';
            } else {
                node.style.opacity = '1';
                node.style.filter = 'none';
            }
        }

        // 初始化思维导图
        function initMindMap() {
            const container = document.getElementById('mindmap-content');
            container.innerHTML = '';

            // 创建根节点
            const rootNode = createNode(mindMapData, 0, 0, 'root-node');
            container.appendChild(rootNode);

            // 等待DOM更新后创建子节点
            setTimeout(() => {
                createChildNodes(mindMapData, rootNode, container, 0);
            }, 100);

            // 隐藏加载提示
            document.getElementById('loading').style.display = 'none';
        }

        // 控制函数
        function expandAll() {
            const nodes = document.querySelectorAll('.node');
            nodes.forEach(node => {
                node.dataset.expanded = 'true';
                node.style.opacity = '1';
            });
        }

        function collapseAll() {
            const nodes = document.querySelectorAll('.node');
            nodes.forEach(node => {
                if (!node.classList.contains('root-node')) {
                    node.dataset.expanded = 'false';
                    node.style.opacity = '0.7';
                }
            });
        }

        function resetView() {
            currentScale = 1;
            currentTranslate = { x: 0, y: 0 };
            updateTransform();
        }

        function zoomIn() {
            currentScale = Math.min(currentScale * 1.2, 3);
            updateTransform();
        }

        function zoomOut() {
            currentScale = Math.max(currentScale / 1.2, 0.3);
            updateTransform();
        }

        function updateTransform() {
            const content = document.getElementById('mindmap-content');
            content.style.transform = `translate(${currentTranslate.x}px, ${currentTranslate.y}px) scale(${currentScale})`;
        }

        // 拖拽功能
        function initDragAndZoom() {
            const mindmap = document.getElementById('mindmap');

            mindmap.addEventListener('mousedown', (e) => {
                isDragging = true;
                dragStart.x = e.clientX - currentTranslate.x;
                dragStart.y = e.clientY - currentTranslate.y;
                mindmap.style.cursor = 'grabbing';
            });

            document.addEventListener('mousemove', (e) => {
                if (!isDragging) return;

                currentTranslate.x = e.clientX - dragStart.x;
                currentTranslate.y = e.clientY - dragStart.y;
                updateTransform();
            });

            document.addEventListener('mouseup', () => {
                isDragging = false;
                mindmap.style.cursor = 'grab';
            });

            // 鼠标滚轮缩放
            mindmap.addEventListener('wheel', (e) => {
                e.preventDefault();
                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                currentScale = Math.max(0.3, Math.min(3, currentScale * delta));
                updateTransform();
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMindMap();
            initDragAndZoom();
        });
